import { messaging, getToken, onMessage } from '../config/firebase';

/**
 * FCM Service for managing Firebase Cloud Messaging tokens and notifications
 * This service handles FCM token generation, registration, and message handling
 */
class FCMService {
  constructor() {
    this.currentToken = null;
    this.isSupported = this.checkSupport();
    this.messageHandlers = [];
  }

  /**
   * Check if FCM is supported in the current environment
   */
  checkSupport() {
    try {
      return 'serviceWorker' in navigator && 'Notification' in window && messaging !== null;
    } catch (error) {
      console.warn('FCM not supported:', error);
      return false;
    }
  }

  /**
   * Request notification permission from the user
   */
  async requestPermission() {
    try {
      if (!this.isSupported) {
        throw new Error('FCM not supported in this environment');
      }

      const permission = await Notification.requestPermission();
      console.log('Notification permission:', permission);
      
      if (permission === 'granted') {
        console.log('Notification permission granted.');
        return true;
      } else {
        console.log('Notification permission denied.');
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }

  /**
   * Generate and retrieve FCM token
   */
  async generateToken() {
    try {
      if (!this.isSupported) {
        console.warn('FCM not supported, returning null token');
        return null;
      }

      // Request permission first
      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        console.warn('Notification permission not granted, cannot generate FCM token');
        return null;
      }

      // Register service worker with error handling
      try {
        await this.registerServiceWorker();
      } catch (swError) {
        console.error('Service worker registration failed:', swError);
        // Continue without service worker for basic functionality
      }

      // Generate token with retry logic
      let token = null;
      const maxRetries = 3;
      let retryCount = 0;

      while (!token && retryCount < maxRetries) {
        try {
          token = await getToken(messaging, {
            vapidKey: 'BNRhAjNoDm5MUeAAsx5c56YqNVmmGypzxm4sbXG17NpKDOvAfui5glK5zh9DA3i4rX7UxdLr23SmoLss5w-1P1Y' // This should be configured in Firebase Console
          });

          if (token) {
            console.log('FCM token generated:', token.substring(0, 20) + '...');
            this.currentToken = token;

            // Store token in localStorage for persistence
            localStorage.setItem('fcmToken', token);
            localStorage.setItem('fcmTokenTimestamp', Date.now().toString());

            return token;
          }
        } catch (tokenError) {
          retryCount++;
          console.warn(`FCM token generation attempt ${retryCount} failed:`, tokenError);

          if (retryCount < maxRetries) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          }
        }
      }

      if (!token) {
        console.warn('Failed to generate FCM token after all retries');
        return null;
      }

      return token;
    } catch (error) {
      console.error('Error generating FCM token:', error);

      // Try to return cached token if available
      const cachedToken = localStorage.getItem('fcmToken');
      const tokenTimestamp = localStorage.getItem('fcmTokenTimestamp');

      if (cachedToken && tokenTimestamp) {
        const tokenAge = Date.now() - parseInt(tokenTimestamp);
        // Use cached token if it's less than 24 hours old
        if (tokenAge < 24 * 60 * 60 * 1000) {
          console.log('Using cached FCM token due to generation error');
          this.currentToken = cachedToken;
          return cachedToken;
        }
      }

      return null;
    }
  }

  /**
   * Register service worker for FCM
   */
  async registerServiceWorker() {
    try {
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
        console.log('Service Worker registered:', registration);
        return registration;
      }
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      throw error;
    }
  }

  /**
   * Get current FCM token (from memory or localStorage)
   */
  getCurrentToken() {
    if (this.currentToken) {
      return this.currentToken;
    }

    // Try to get from localStorage
    const storedToken = localStorage.getItem('fcmToken');
    if (storedToken) {
      this.currentToken = storedToken;
      return storedToken;
    }

    return null;
  }

  /**
   * Refresh FCM token
   */
  async refreshToken() {
    try {
      console.log('Refreshing FCM token...');
      const newToken = await this.generateToken();
      
      if (newToken && newToken !== this.currentToken) {
        console.log('FCM token refreshed');
        // Here you could send the new token to your server
        this.onTokenRefresh(newToken);
      }
      
      return newToken;
    } catch (error) {
      console.error('Error refreshing FCM token:', error);
      return null;
    }
  }

  /**
   * Handle token refresh (override this method to send token to server)
   */
  onTokenRefresh(newToken) {
    console.log('FCM token refreshed:', newToken.substring(0, 20) + '...');
    // Override this method to handle token refresh
  }

  /**
   * Set up foreground message handling
   */
  setupMessageHandling() {
    if (!this.isSupported) {
      return;
    }

    try {
      onMessage(messaging, (payload) => {
        console.log('Message received in foreground:', payload);
        
        // Handle the message
        this.handleForegroundMessage(payload);
        
        // Notify all registered handlers
        this.messageHandlers.forEach(handler => {
          try {
            handler(payload);
          } catch (error) {
            console.error('Error in message handler:', error);
          }
        });
      });
    } catch (error) {
      console.error('Error setting up message handling:', error);
    }
  }

  /**
   * Handle foreground messages
   */
  handleForegroundMessage(payload) {
    console.log('Handling foreground message:', payload);
    
    // Show notification for incoming calls
    if (payload.data?.type === 'incoming_call') {
      this.showCallNotification(payload);
    }
  }

  /**
   * Show call notification
   */
  showCallNotification(payload) {
    try {
      const title = payload.notification?.title || 'Incoming Call';
      const body = payload.notification?.body || 'You have an incoming call';
      
      if (Notification.permission === 'granted') {
        const notification = new Notification(title, {
          body: body,
          icon: '/logo192.png',
          tag: 'incoming-call',
          requireInteraction: true,
          data: payload.data
        });

        notification.onclick = () => {
          console.log('Notification clicked');
          window.focus();
          notification.close();
        };
      }
    } catch (error) {
      console.error('Error showing call notification:', error);
    }
  }

  /**
   * Add message handler
   */
  addMessageHandler(handler) {
    if (typeof handler === 'function') {
      this.messageHandlers.push(handler);
    }
  }

  /**
   * Remove message handler
   */
  removeMessageHandler(handler) {
    const index = this.messageHandlers.indexOf(handler);
    if (index > -1) {
      this.messageHandlers.splice(index, 1);
    }
  }

  /**
   * Clear stored token
   */
  clearToken() {
    this.currentToken = null;
    localStorage.removeItem('fcmToken');
  }

  /**
   * Initialize FCM service
   */
  async initialize() {
    try {
      console.log('Initializing FCM Service...');
      
      if (!this.isSupported) {
        console.warn('FCM not supported, skipping initialization');
        return false;
      }

      // Set up message handling
      this.setupMessageHandling();
      
      // Try to get existing token or generate new one
      let token = this.getCurrentToken();
      if (!token) {
        token = await this.generateToken();
      }

      console.log('FCM Service initialized successfully');
      return !!token;
    } catch (error) {
      console.error('Error initializing FCM Service:', error);
      return false;
    }
  }
}

// Create singleton instance
const fcmService = new FCMService();

export default fcmService;
