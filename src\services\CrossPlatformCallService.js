import axios from "axios";
import { generateChannelName, validateChannelName } from "../utils/agoraConfig";

const API_URL = "http://meetagora-hfdjcgbcdrepbuc0.southeastasia-01.azurewebsites.net/";

const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

/**
 * CrossPlatformCallService for managing cross-platform voice and video calls
 * This service handles web-to-mobile and mobile-to-web calling using FCM notifications
 */
export class CrossPlatformCallService {
  constructor() {
    this.activeCallRequests = new Map(); // Track active call requests
    this.callTimeouts = new Map(); // Track call timeouts
  }

  /**
   * Initiate a cross-platform call using the calls/send-call endpoint
   */
  async initiateCall({
    callerId,
    calleeId,
    callerName,
    calleeName,
    eventCode,
    isVideoCall = false,
    callType = 'voice' // 'voice' or 'video'
  }) {
    try {
      console.log('Initiating cross-platform call:', {
        callerId,
        calleeId,
        callerName,
        calleeName,
        eventCode,
        isVideoCall,
        callType
      });

      // Validate required parameters
      if (!callerId || !calleeId || !eventCode) {
        const error = new Error('Missing required parameters: callerId, calleeId, or eventCode');
        error.code = 'MISSING_PARAMETERS';
        throw error;
      }

      // Validate parameter types and formats
      if (typeof callerId !== 'string' || typeof calleeId !== 'string') {
        const error = new Error('callerId and calleeId must be strings');
        error.code = 'INVALID_PARAMETER_TYPE';
        throw error;
      }

      if (typeof eventCode !== 'string' || eventCode.length === 0) {
        const error = new Error('eventCode must be a non-empty string');
        error.code = 'INVALID_EVENT_CODE';
        throw error;
      }

      // Generate channel name for the call
      const channelName = generateChannelName(callerId, calleeId, callType);
      
      // Validate channel name
      const validation = validateChannelName(channelName);
      if (!validation.isValid) {
        throw new Error(`Invalid channel name: ${validation.error}`);
      }

      // Generate unique call ID
      const callId = `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Prepare request payload for calls/send-call endpoint
      const requestPayload = {
        callerId: callerId, // Use agoraid as specified in requirements
        calleeId: calleeId, // Use agoraid as specified in requirements
        eventCode: eventCode, // Extract from eventData as specified
        channelName: channelName,
        callId: callId,
        callerName: callerName,
        calleeName: calleeName,
        isVideoCall: isVideoCall,
        callType: callType,
        timestamp: Date.now()
      };

      console.log('Sending call request to backend:', requestPayload);

      // Call the backend endpoint with timeout and retry logic
      let response;
      const maxRetries = 3;
      let retryCount = 0;

      while (retryCount < maxRetries) {
        try {
          response = await Promise.race([
            api.post('/calls/send-call', requestPayload),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Request timeout')), 10000)
            )
          ]);
          break; // Success, exit retry loop
        } catch (error) {
          retryCount++;
          console.warn(`Backend request attempt ${retryCount} failed:`, error.message);

          if (retryCount >= maxRetries) {
            const backendError = new Error(`Backend request failed after ${maxRetries} attempts: ${error.message}`);
            backendError.code = 'BACKEND_REQUEST_FAILED';
            backendError.originalError = error;
            throw backendError;
          }

          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1)));
        }
      }

      if (!response || !response.data) {
        const error = new Error('Invalid response from calls/send-call endpoint');
        error.code = 'INVALID_BACKEND_RESPONSE';
        throw error;
      }

      const {
        callerFCMToken,
        calleeFCMToken,
        rtcToken,
        jwtToken
      } = response.data;

      console.log('Received call details from backend:', {
        hasCallerFCMToken: !!callerFCMToken,
        hasCalleeFCMToken: !!calleeFCMToken,
        hasRtcToken: !!rtcToken,
        hasJwtToken: !!jwtToken
      });

      // Store call request for tracking
      this.activeCallRequests.set(callId, {
        callId,
        callerId,
        calleeId,
        callerName,
        calleeName,
        channelName,
        isVideoCall,
        callType,
        callerFCMToken,
        calleeFCMToken,
        rtcToken,
        jwtToken,
        timestamp: Date.now(),
        status: 'initiated'
      });

      // Send FCM notification to mobile device if callee has FCM token
      if (calleeFCMToken) {
        console.log('Sending FCM notification to mobile device...');
        await this.sendFCMNotification({
          fcmToken: calleeFCMToken,
          callId,
          callerId,
          callerName,
          channelName,
          isVideoCall,
          rtcToken,
          jwtToken
        });
      } else {
        console.log('No FCM token for callee, assuming web-to-web call');
      }

      // Set timeout for call (30 seconds)
      const timeout = setTimeout(() => {
        this.handleCallTimeout(callId);
      }, 30000);

      this.callTimeouts.set(callId, timeout);

      return {
        success: true,
        callId,
        channelName,
        rtcToken,
        jwtToken,
        callerFCMToken,
        calleeFCMToken,
        isWebToMobile: !!calleeFCMToken
      };

    } catch (error) {
      console.error('Error initiating cross-platform call:', error);
      throw error;
    }
  }

  /**
   * Send FCM notification to mobile device
   */
  async sendFCMNotification({
    fcmToken,
    callId,
    callerId,
    callerName,
    channelName,
    isVideoCall,
    rtcToken,
    jwtToken
  }) {
    try {
      console.log('Sending FCM notification:', {
        fcmToken: fcmToken.substring(0, 20) + '...',
        callId,
        callerId,
        callerName,
        isVideoCall
      });

      // Prepare FCM notification payload
      const notificationPayload = {
        to: fcmToken,
        notification: {
          title: `Incoming ${isVideoCall ? 'Video' : 'Voice'} Call`,
          body: `${callerName} is calling you`,
          icon: '/logo192.png',
          badge: '/logo192.png',
          tag: 'incoming-call',
          requireInteraction: true
        },
        data: {
          type: 'incoming_call',
          callId: callId,
          callerId: callerId,
          callerName: callerName,
          channelName: channelName,
          isVideoCall: isVideoCall.toString(),
          rtcToken: rtcToken,
          jwtToken: jwtToken,
          timestamp: Date.now().toString()
        },
        android: {
          priority: 'high',
          notification: {
            channel_id: 'incoming_calls',
            priority: 'high',
            default_sound: true,
            default_vibrate: true
          }
        },
        apns: {
          headers: {
            'apns-priority': '10'
          },
          payload: {
            aps: {
              alert: {
                title: `Incoming ${isVideoCall ? 'Video' : 'Voice'} Call`,
                body: `${callerName} is calling you`
              },
              sound: 'default',
              badge: 1,
              'content-available': 1
            }
          }
        }
      };

      // Send FCM notification through backend
      const response = await api.post('/notifications/send-fcm', notificationPayload);

      if (response.data.success) {
        console.log('FCM notification sent successfully');
        return true;
      } else {
        console.error('Failed to send FCM notification:', response.data.error);
        return false;
      }

    } catch (error) {
      console.error('Error sending FCM notification:', error);
      return false;
    }
  }

  /**
   * Handle call acceptance from mobile device
   */
  handleCallAccepted(callId) {
    console.log('Call accepted:', callId);
    
    const callRequest = this.activeCallRequests.get(callId);
    if (callRequest) {
      callRequest.status = 'accepted';
      this.activeCallRequests.set(callId, callRequest);
      
      // Clear timeout
      const timeout = this.callTimeouts.get(callId);
      if (timeout) {
        clearTimeout(timeout);
        this.callTimeouts.delete(callId);
      }
    }
  }

  /**
   * Handle call rejection from mobile device
   */
  handleCallRejected(callId) {
    console.log('Call rejected:', callId);
    
    const callRequest = this.activeCallRequests.get(callId);
    if (callRequest) {
      callRequest.status = 'rejected';
      this.activeCallRequests.set(callId, callRequest);
      
      // Clear timeout
      const timeout = this.callTimeouts.get(callId);
      if (timeout) {
        clearTimeout(timeout);
        this.callTimeouts.delete(callId);
      }
      
      // Clean up after a delay
      setTimeout(() => {
        this.activeCallRequests.delete(callId);
      }, 5000);
    }
  }

  /**
   * Handle call timeout
   */
  handleCallTimeout(callId) {
    console.log('Call timed out:', callId);
    
    const callRequest = this.activeCallRequests.get(callId);
    if (callRequest) {
      callRequest.status = 'timeout';
      this.activeCallRequests.set(callId, callRequest);
      
      // Clean up
      this.callTimeouts.delete(callId);
      
      // Clean up after a delay
      setTimeout(() => {
        this.activeCallRequests.delete(callId);
      }, 5000);
    }
  }

  /**
   * Cancel an active call
   */
  cancelCall(callId) {
    console.log('Cancelling call:', callId);
    
    const callRequest = this.activeCallRequests.get(callId);
    if (callRequest) {
      callRequest.status = 'cancelled';
      
      // Clear timeout
      const timeout = this.callTimeouts.get(callId);
      if (timeout) {
        clearTimeout(timeout);
        this.callTimeouts.delete(callId);
      }
      
      // Send cancellation notification if it's a mobile call
      if (callRequest.calleeFCMToken) {
        this.sendCallCancellationNotification(callRequest);
      }
      
      // Clean up
      this.activeCallRequests.delete(callId);
    }
  }

  /**
   * Send call cancellation notification
   */
  async sendCallCancellationNotification(callRequest) {
    try {
      const notificationPayload = {
        to: callRequest.calleeFCMToken,
        data: {
          type: 'call_cancelled',
          callId: callRequest.callId,
          callerId: callRequest.callerId,
          callerName: callRequest.callerName
        }
      };

      await api.post('/notifications/send-fcm', notificationPayload);
      console.log('Call cancellation notification sent');
    } catch (error) {
      console.error('Error sending call cancellation notification:', error);
    }
  }

  /**
   * Get active call request
   */
  getCallRequest(callId) {
    return this.activeCallRequests.get(callId);
  }

  /**
   * Check if a call is cross-platform (web-to-mobile)
   */
  isCrossPlatformCall(callId) {
    const callRequest = this.activeCallRequests.get(callId);
    return callRequest && !!callRequest.calleeFCMToken;
  }

  /**
   * Clean up all active calls
   */
  cleanup() {
    console.log('Cleaning up CrossPlatformCallService...');
    
    // Clear all timeouts
    this.callTimeouts.forEach(timeout => clearTimeout(timeout));
    this.callTimeouts.clear();
    
    // Clear all active requests
    this.activeCallRequests.clear();
  }
}

// Create singleton instance
const crossPlatformCallService = new CrossPlatformCallService();

export default crossPlatformCallService;
